<?php
/**
 * demir Class
 *
 * <AUTHOR>
 * @since    1.0.0
 * @package  demir
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( ! class_exists( 'demir' ) ) :

	/**
	 * The main demir class
	 */
	class demir {

		/**
		 * Setup class.
		 *
		 * @since 1.0
		 */
		public function __construct() {
			add_action( 'init', array( $this, 'load_translations' ) );
			add_action( 'after_setup_theme', array( $this, 'setup' ) );
			add_action( 'widgets_init', array( $this, 'widgets_init' ) );
			add_action( 'wp_enqueue_scripts', array( $this, 'scripts' ), 10 );
			add_action( 'wp_enqueue_scripts', array( $this, 'child_scripts' ), 30 ); // After WooCommerce.
			add_filter( 'body_class', array( $this, 'body_classes' ) );
			add_filter( 'wp_page_menu_args', array( $this, 'page_menu_args' ) );
			add_filter( 'wp_get_attachment_image_attributes', array( $this, 'commercegurus_custom_img_sizes' ), 10, 3 );
		}

		/**
		 * Load translations for the theme.
		 *
		 * @since 1.0
		 */
		/*public function load_translations() {
			load_theme_textdomain( 'demir', trailingslashit( WP_LANG_DIR ) . 'themes/' );
			load_theme_textdomain( 'demir', get_stylesheet_directory() . '/languages' );
			load_theme_textdomain( 'demir', get_template_directory() . '/languages' );
		}*/

		public function load_translations() {
		    // Priority 1: Load translations from WP_LANG_DIR if available.
		    if (file_exists(trailingslashit(WP_LANG_DIR) . 'themes/demir.mo')) {
		        load_theme_textdomain('demir', trailingslashit(WP_LANG_DIR) . 'themes/');
		    }
		    // Priority 2: Load translations from the child theme if available.
		    elseif (file_exists(get_stylesheet_directory() . '/languages/demir.mo')) {
		        load_theme_textdomain('demir', get_stylesheet_directory() . '/languages');
		    }
		    // Priority 3: Fallback to the parent theme's languages directory.
		    else {
		        load_theme_textdomain('demir', get_template_directory() . '/languages');
		    }
		}

		/**
		 * Custom sizes attribute for PDP images.
		 *
		 * @since 2.4.2
		 *
		 * @param array        $attr       Array of attribute values for the image markup, keyed by attribute name.
		 *                                 See wp_get_attachment_image().
		 * @param WP_Post      $attachment Image attachment post.
		 * @param string|array $size       Requested size. Image size or array of width and height values
		 *                                 (in that order). Default 'thumbnail'.
		 *
		 * @return array
		 */
		public function commercegurus_custom_img_sizes( $attr, $attachment, $size ) {
		    if ( is_admin() ) {
		        return $attr;
		    }

		    // some images might be missing a sizes attribute so let's check for it so we don't trigger notices.
		    if ( ! isset( $attr['sizes'] ) ) {
		        $attr['sizes'] = '';
		    }

		    if ( 'woocommerce_single' === $size ) {
		        $attr['sizes'] = '(max-width: 360px) 330px, ' . $attr['sizes'];
		    } elseif ( 'woocommerce_gallery_thumbnail' === $size ) {
		        $attr['sizes'] = '(max-width: 360px) 75px, ' . $attr['sizes'];
		    } elseif ( 'woocommerce_thumbnail' === $size ) {
		        $attr['sizes'] = '(max-width: 360px) 147px, ' . $attr['sizes'];
		    }
		    
		    return $attr;
		}

		/**
		 * Sets up theme defaults and registers support for various WordPress features.
		 *
		 * Note that this function is hooked into the after_setup_theme hook, which
		 * runs before the init hook. The init hook is too late for some features, such
		 * as indicating support for post thumbnails.
		 */
		public function setup() {
			/**
			 * Add default posts and comments RSS feed links to head.
			 */
			add_theme_support( 'automatic-feed-links' );

			/*
			 * Enable support for Post Thumbnails on posts and pages.
			 *
			 * @link https://developer.wordpress.org/reference/functions/add_theme_support/#Post_Thumbnails
			 */
			add_theme_support( 'post-thumbnails' );

			/**
			 * Enable support for site logo
			 */
			add_theme_support(
				'custom-logo',
				apply_filters(
					'demir_custom_logo_args',
					array(
						'height'      => 110,
						'width'       => 470,
						'flex-height' => true,
						'flex-width'  => true,
					)
				)
			);

			// This theme uses wp_nav_menu() in two locations.
			register_nav_menus(
				apply_filters(
					'demir_register_nav_menus',
					array(
						'primary'   => __( 'Primary Menu', 'demir' ),
						'secondary' => __( 'Secondary Menu', 'demir' ),
						'mobile' => __( 'Mobile Menu', 'demir' ),
					)
				)
			);

			/*
			 * Switch default core markup for search form, comment form, comments, galleries, captions and widgets
			 * to output valid HTML5.
			 */
			add_theme_support(
				'html5',
				apply_filters(
					'demir_html5_args',
					array(
						'search-form',
						'comment-form',
						'comment-list',
						'gallery',
						'caption',
						'widgets',
					)
				)
			);

			// Declare WooCommerce support.
			add_theme_support(
				'woocommerce',
				apply_filters(
					'demir_woocommerce_args',
					array(

						'product_grid' => array(
							'default_columns' => 3,
							'default_rows'    => 4,
							'min_columns'     => 1,
							'max_columns'     => 6,
							'min_rows'        => 1,
						),
					)
				)
			);

			add_filter(
				'woocommerce_get_image_size_gallery_thumbnail',
				function( $size ) {
					return array(
						'width'  => 150,
						'height' => 9999,
						'crop'   => 0,
					);
				}
			);

			// update_option( 'woocommerce_thumbnail_cropping', 'uncropped' );
			add_theme_support( 'wc-product-gallery-zoom' );
			add_theme_support( 'wc-product-gallery-lightbox' );
			add_theme_support( 'wc-product-gallery-slider' );

			// Declare support for title theme feature.
			add_theme_support( 'title-tag' );

			// Declare support for selective refreshing of widgets.
			add_theme_support( 'customize-selective-refresh-widgets' );

			// Declare Gutenberg wide images support.
			add_theme_support( 'align-wide' );

			// Custom thumb for PDP. - Specifically targetted at Moto G4 - PDP images max. out at 330px and the G4 has a devicepixelratio of 3 - therefore 990px is optimal for passing PSI mobile audits.
			add_image_size( 'commercegurus-pdp-large', 990, 9999 );
			add_image_size( 'commercegurus-plp-mobile', 441, 9999 );

		}

		/**
		 * Register widget area.
		 *
		 * @link https://codex.wordpress.org/Function_Reference/register_sidebar
		 */
		public function widgets_init() {
			$sidebar_args['sidebar'] = array(
				'name'        => __( 'Sidebar', 'demir' ),
				'id'          => 'sidebar-1',
				'description' => 'The WooCommerce archives sidebar.',
			);

			$sidebar_args['sidebar-posts'] = array(
				'name'        => __( 'Sidebar Posts', 'demir' ),
				'id'          => 'sidebar-posts',
				'description' => __( 'The posts sidebar.', 'demir' ),
			);

			$sidebar_args['sidebar-pages'] = array(
				'name'        => __( 'Sidebar Pages', 'demir' ),
				'id'          => 'sidebar-pages',
				'description' => __( 'The pages sidebar.', 'demir' ),
			);

			$sidebar_args['header'] = array(
				'name'        => __( 'Below Header', 'demir' ),
				'id'          => 'header-1',
				'description' => __( 'Widgets added to this region will appear beneath the header and above the main content.', 'demir' ),
			);

			$sidebar_args['top-bar-left'] = array(
				'name'          => __( 'Top Bar Left', 'demir' ),
				'id'            => 'top-bar-left',
				'description'   => __( 'A widget added to this region will appear at the very top of the site to the left.', 'demir' ),
				'before_widget' => '<div class="top-bar-left  %2$s">',
				'after_widget'  => '</div>',
			);

			$sidebar_args['top-bar'] = array(
				'name'          => __( 'Top Bar Center', 'demir' ),
				'id'            => 'top-bar',
				'description'   => __( 'A widget added to this region will appear at the very top of the site in the center.', 'demir' ),
				'before_widget' => '<div class="top-bar-center  %2$s">',
				'after_widget'  => '</div>',
			);

			$sidebar_args['top-bar-right'] = array(
				'name'          => __( 'Top Bar Right', 'demir' ),
				'id'            => 'top-bar-right',
				'description'   => __( 'A widget added to this region will appear at the very top of the site to the right.', 'demir' ),
				'before_widget' => '<div class="top-bar-right  %2$s">',
				'after_widget'  => '</div>',
			);

			$sidebar_args['single-product-field'] = array(
				'name'        => __( 'Single Product Custom Area', 'demir' ),
				'id'          => 'single-product-field',
				'description' => __( 'A widget added to this region will appear below the "Add to cart" button on a product page.', 'demir' ),
			);

			$sidebar_args['floating-button-content'] = array(
				'name'        => __( 'Floating Button Modal Content', 'demir' ),
				'id'          => 'floating-button-content',
				'description' => __( 'A widget added to this region will appear within a modal window on a single product page. It is intended for a form shortcode, e.g. Contact Form 7 - but you can add any content you wish.', 'demir' ),
			);

			$sidebar_args['cart-summary'] = array(
				'name'        => __( 'Below Cart Summary', 'demir' ),
				'id'          => 'cart-summary',
				'description' => __( 'A widget added to this region will appear below the cart summary.', 'demir' ),
			);

			$sidebar_args['cart-field'] = array(
				'name'        => __( 'Cart Custom Area', 'demir' ),
				'id'          => 'cart-field',
				'description' => __( 'A widget added to this region will appear below the "Proceed to checkout" button on the Cart page.', 'demir' ),
			);

			$sidebar_args['checkout-field'] = array(
				'name'        => __( 'Checkout Custom Area', 'demir' ),
				'id'          => 'checkout-field',
				'description' => __( 'A widget added to this region will appear below the "Place order" button on the Checkout page.', 'demir' ),
			);

			$sidebar_args['thankyou-field'] = array(
				'name'        => __( 'Thank You Custom Area', 'demir' ),
				'id'          => 'thankyou-field',
				'description' => __( 'A widget added to this region will appear at the bottom of the thank you page after an order has been placed.', 'demir' ),
			);

			$sidebar_args['below-content'] = array(
				'name'        => __( 'Below Content', 'demir' ),
				'id'          => 'below-content',
				'description' => __( 'A widget added to this region will appear below the main content area.', 'demir' ),
			);

			$sidebar_args['footer'] = array(
				'name'        => __( 'Footer', 'demir' ),
				'id'          => 'footer',
				'description' => __( 'A widget added to this region will appear within the footer area.', 'demir' ),
			);

			$sidebar_args['copyright'] = array(
				'name'        => __( 'Copyright', 'demir' ),
				'id'          => 'copyright',
				'description' => __( 'A widget added to this region will appear within the copyright area.', 'demir' ),
			);

			$sidebar_args['mobile-extra'] = array(
				'name'        => __( 'Mobile Extra', 'demir' ),
				'id'          => 'mobile-extra',
				'description' => __( 'A widget added to this region will appear below the mobile navigation area.', 'demir' ),
			);

			$sidebar_args['empty-mini-cart'] = array(
				'name'        => __( 'Empty Mini Cart', 'demir' ),
				'id'          => 'empty-mini-cart',
				'description' => __( 'A widget added to this region will appear within the mini cart if there are no products in it.', 'demir' ),
			);

			$sidebar_args = apply_filters( 'demir_sidebar_args', $sidebar_args );

			foreach ( $sidebar_args as $sidebar => $args ) {
				$widget_tags = array(
					'before_widget' => '<div id="%1$s" class="widget %2$s">',
					'after_widget'  => '</div>',
					'before_title'  => '<span class="gamma widget-title">',
					'after_title'   => '</span>',
				);

				$filter_hook = sprintf( 'demir_%s_widget_tags', $sidebar );
				$widget_tags = apply_filters( $filter_hook, $widget_tags );

				if ( is_array( $widget_tags ) ) {
					register_sidebar( $args + $widget_tags );
				}
			}
		}

		/**
		 * Enqueue scripts and styles.
		 *
		 * @since  1.0.0
		 */
		public function scripts() {
			global $demir_version;

			$demir_general_speed_rivolicons = '';
			$demir_general_speed_rivolicons = demir_get_option( 'demir_general_speed_rivolicons' );

			/**
			 * Styles
			 */
			if ( 'yes' === $demir_general_speed_rivolicons ) {
				wp_enqueue_style( 'demir-rivolicons', get_template_directory_uri() . '/assets/css/base/rivolicons.css', '', $demir_version );
			}

			/**
			 * Scripts
			 */
			$suffix = ( defined( 'SCRIPT_DEBUG' ) && SCRIPT_DEBUG ) ? '' : '.min';

			if ( is_singular() && comments_open() && get_option( 'thread_comments' ) ) {
				wp_enqueue_script( 'comment-reply' );
			}
		}

		/**
		 * Enqueue child theme stylesheet.
		 * A separate function is required as the child theme css needs to be enqueued _after_ the parent theme
		 * primary css and the separate WooCommerce css.
		 *
		 * @since  1.0.0
		 */
		public function child_scripts() {
			if ( is_child_theme() ) {
				$child_theme = wp_get_theme( get_stylesheet() );
				wp_enqueue_style( 'demir-child-style', get_stylesheet_uri(), array(), $child_theme->get( 'Version' ) );
			}
		}

		/**
		 * Get our wp_nav_menu() fallback, wp_page_menu(), to show a home link.
		 *
		 * @param array $args Configuration arguments.
		 * @return array
		 */
		public function page_menu_args( $args ) {
			$args['show_home'] = true;
			return $args;
		}

		/**
		 * Adds custom classes to the array of body classes.
		 *
		 * @param array $classes Classes for the body element.
		 * @return array
		 */
		public function body_classes( $classes ) {

			// Adds a class if breadcrumbs are turned off.
			$demir_layout_woocommerce_display_breadcrumbs = '';
			$demir_layout_woocommerce_display_breadcrumbs = demir_get_option( 'demir_layout_woocommerce_display_breadcrumbs' );

			if ( false === $demir_layout_woocommerce_display_breadcrumbs ) {
				$classes[] = 'no-breadcrumbs';
			}

			// If the main sidebar doesn't contain widgets, adjust the layout to be full-width.
			if ( ! is_active_sidebar( 'sidebar-1' ) ) {
				$classes[] = 'demir-full-width-content';
			}

			$demir_layout_singlepost = '';
			$demir_layout_singlepost = demir_get_option( 'demir_layout_singlepost' );

			$demir_header_layout = '';
			$demir_header_layout = demir_get_option( 'demir_header_layout' );

			if ( isset( $_GET['header'] ) ) {
				$demir_header_layout = $_GET['header'];
			}

			$demir_layout_woocommerce_sticky_cart_position = '';
			$demir_layout_woocommerce_sticky_cart_position = demir_get_option( 'demir_layout_woocommerce_sticky_cart_position' );

			$demir_sticky_mobile_header = '';
			$demir_sticky_mobile_header = demir_get_option( 'demir_sticky_mobile_header' );

			$demir_sticky_header = '';
			$demir_sticky_header = demir_get_option( 'demir_sticky_header' );

			$demir_header_layout_container = '';
			$demir_header_layout_container = demir_get_option( 'demir_header_layout_container' );

			if ( isset( $_GET['headercontainer'] ) ) {
				$demir_header_layout_container = $_GET['headercontainer'];
			}

			$demir_layout_wrapper = '';
			$demir_layout_wrapper = demir_get_option( 'demir_layout_wrapper' );

			$demir_layout_woocommerce_cta_display = '';
			$demir_layout_woocommerce_cta_display = demir_get_option( 'demir_layout_woocommerce_cta_display' );

			$demir_layout_woocommerce_card_display = '';
			$demir_layout_woocommerce_card_display = demir_get_option( 'demir_layout_woocommerce_card_display' );

			$demir_search_mobile = '';
			$demir_search_mobile = demir_get_option( 'demir_search_mobile' );

			$demir_search_mobile_position = '';
			$demir_search_mobile_position = demir_get_option( 'demir_search_mobile_position' );

			$demir_layout_woocommerce_sticky_cart_display = '';
			$demir_layout_woocommerce_sticky_cart_display = demir_get_option( 'demir_layout_woocommerce_sticky_cart_display' );

			$demir_layout_woocommerce_mobile_grid = '';
			$demir_layout_woocommerce_mobile_grid = demir_get_option( 'demir_layout_woocommerce_mobile_grid' );

			$demir_layout_woocommerce_category_position = '';
			$demir_layout_woocommerce_category_position = demir_get_option( 'demir_layout_woocommerce_category_position' );

			if ( isset( $_GET['productcard'] ) ) {
				$demir_layout_woocommerce_card_display = $_GET['productcard'];
			}


			if ( 'slide' === $demir_layout_woocommerce_card_display ) {
				$classes[] = 'product-card__slide';
			}

			if ( 'static' === $demir_layout_woocommerce_cta_display ) {
				$classes[] = 'static-cta-buttons';
			}

			if ( 'no-cta' === $demir_layout_woocommerce_cta_display ) {
				$classes[] = 'no-cta-buttons';
			}

			if ( 'enable' === $demir_sticky_mobile_header ) {
				$classes[] = 'sticky-m';
			}

			if ( 'enable' === $demir_sticky_header ) {
				$classes[] = 'sticky-d';
			}	

			if ( ( 'below-header' === $demir_search_mobile_position ) && ( 'enable' === $demir_search_mobile ) ) {
				$classes[] = 'm-search-bh';
			}

			if ( 'mobile-grid-one' === $demir_layout_woocommerce_mobile_grid ) {
				$classes[] = 'm-grid-1';
			}

			if ( 'mobile-grid-two' === $demir_layout_woocommerce_mobile_grid ) {
				$classes[] = 'm-grid-2';
			}

			// Page container
			if ( 'yes' === $demir_layout_wrapper ) {
				$classes[] = 'demir-contained';
			}

			// Add a body class if alternative header layouts are selected.
			if ( 'header-2' === $demir_header_layout ) {
				$classes[] = 'header-2';
			}
			if ( 'header-3' === $demir_header_layout ) {
				$classes[] = 'header-3';
			}
			if ( 'header-4' === $demir_header_layout ) {
				$classes[] = 'header-4';
			}
			if ( 'header-5' === $demir_header_layout ) {
				$classes[] = 'header-5';
			}
			if ( 'full-width-header' === $demir_header_layout_container ) {
				$classes[] = 'full-width-header';
			}

			// Add a body class if sticky bar bottom position is selected.
			if ( true === $demir_layout_woocommerce_sticky_cart_display ) {
				if ( 'bottom' === $demir_layout_woocommerce_sticky_cart_position ) {
					$classes[] = 'sticky-b';
				}
			}

			// Add a body class if sticky bar top position is selected.
			if ( true === $demir_layout_woocommerce_sticky_cart_display ) {
				if ( 'top' === $demir_layout_woocommerce_sticky_cart_position ) {
					$classes[] = 'sticky-t';
				}
			}

			// Add a class if the blog layout 2 is selected.
			if ( 'singlepost-layout-two' === $demir_layout_singlepost ) {
				$classes[] = 'post-l2';
			}

			// Add a class if the PLP layout is set to below header.
			if ( 'below-header' === $demir_layout_woocommerce_category_position ) {
				$classes[] = 'plp-below';
			}

			return $classes;
		}

		/**
		 * Custom navigation markup template hooked into `navigation_markup_template` filter hook.
		 */
		public function navigation_markup_template() {
			$template  = '<nav id="post-navigation" class="navigation %1$s" aria-label="' . esc_html__( 'Post Navigation', 'demir' ) . '">';
			$template .= '<span class="screen-reader-text">%2$s</span>';
			$template .= '<div class="nav-links">%3$s</div>';
			$template .= '</nav>';

			return apply_filters( 'demir_navigation_markup_template', $template );
		}

	}
endif;

return new demir();


