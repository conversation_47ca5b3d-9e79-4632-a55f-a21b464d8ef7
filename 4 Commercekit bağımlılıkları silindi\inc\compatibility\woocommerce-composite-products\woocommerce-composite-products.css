/* demir WooCommerce Composite Products Styling: https://woocommerce.com/products/composite-products */

.composite_summary,
.composite_wrap {
    margin-right: auto;
    margin-left: auto;
    padding-right: 2.617924em;
    padding-left: 2.617924em;
}

.summary .composite_wrap {
    padding-left: 0;
    padding-right: 0;
}

.product .cart .composite_price {
    padding-bottom: 10px;
}

.product .composite_button {
    padding-bottom: 30px;
}

.product .composite_form {
    padding-left: 0px !important;
}

.product .composite_navigation.componentized .page_button {
    font-size: 14px;
}

.product .composite_form .component_title_toggled {
    margin-bottom: 0;
}

.composite_component .details.component_data p {
    font-size: 14px;
}

.composite_component .button.component_option_thumbnail_select {
    font-size: 14px;
}

.composite_form:not(.paged) .composite_wrap {
    padding-top: 1rem;
}

@media (max-width: 992px) {
    .composite_summary,
  .composite_wrap,
    .composite_component {
        padding-right: 1em;
        padding-left: 1em;
    }
    .composite_component {
        padding-bottom: 1em;
    }
}
