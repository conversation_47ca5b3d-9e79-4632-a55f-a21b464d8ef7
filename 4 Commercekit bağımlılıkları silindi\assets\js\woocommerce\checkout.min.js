document.addEventListener("DOMContentLoaded",function(){function a(){a._tick&&cancelAnimationFrame(a._tick),a._tick=requestAnimationFrame(function(){a._tick=null;var b=document.querySelector("#order_review"),c=document.querySelector("#order_review_heading"),d=document.querySelector("form.woocommerce-checkout"),e=document.querySelector("#customer_details");if(c&&d&&e){var f=-1,g=-1;document.querySelectorAll(".payment_box").forEach(function(a){var b=a.offsetHeight;f=Math.max(f,b),a.querySelector("input:checked")&&(g=b)});var h=0;document.querySelector(".wc-terms-and-conditions")&&(h=216);var i=b.offsetHeight+h+(f-g+30);if(e.offsetLeft<c.offsetLeft&&c.getBoundingClientRect().top<=0&&window.innerHeight>i){var j=b.offsetWidth,k=d.offsetWidth-j;b.classList.add("payment-fixed"),b.style.width=j+"px",void 0===b._isRTL&&(b._isRTL="rtl"===getComputedStyle(b).direction),b._isRTL?b.style.marginRight=k+"px":b.style.marginLeft=k+"px"}else b.classList.remove("payment-fixed"),b.removeAttribute("style")}})}document.addEventListener("scroll",a),window.addEventListener("resize",a)});

