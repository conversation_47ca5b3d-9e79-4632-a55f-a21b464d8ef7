/* Expand/Collapse WC Product Categories Widget */
.product-categories .children {
    display: none; 
}
.demir-wc-cat-widget--expanded > .children {
    display: block; /* Show nested lists when expanded */
}
.demir-wc-cat-widget--toggle {
    position: absolute;
    top: 1px;
    cursor: pointer;
    display: inline-flex;
    margin-left: .5em;
    transform: rotate(-360deg);
    transition: all 0.3s ease;
    width: 18px;
    height: 18px;
    background: #fff;
    border-radius: 50%;
    box-shadow: rgba(0, 0, 0, 0.05) 0 6px 24px 0, rgba(0, 0, 0, 0.08) 0 0 0 1px;    
    justify-content: center;
    align-items: center;
}
.demir-wc-cat-widget--toggle:before {
    content: url("data:image/svg+xml;charset=utf8,%3Csvg width='12' height='12' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M19 9L12 16L5 9' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
    display: inline-flex;
    justify-content: center;
    align-items: center;
    line-height: 1;
    margin-top: 2px;
}
.demir-wc-cat-widget--expanded > a .demir-wc-cat-widget--toggle {
    transform: rotate(-180deg);
}
