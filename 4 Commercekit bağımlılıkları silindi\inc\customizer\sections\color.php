<?php
/**
 *
 * Kirki color section
 *
 * @package CommerceGurus
 * @subpackage demir
 */
function demir_kirki_section_color( $wp_customize ) {

	// Colors.
	$wp_customize->add_section( 'demir_color_section_topbar', array(
		'title'			 => esc_html__( 'Top Bar', 'demir' ),
		'panel'			 => 'demir_panel_colors',
		'priority'		 => 10,
	) );

	$wp_customize->add_section( 'demir_color_section_header', array(
		'title'			 => esc_html__( 'Header', 'demir' ),
		'panel'			 => 'demir_panel_colors',
		'priority'		 => 10,
	) );

	$wp_customize->add_section( 'demir_color_section_below_header', array(
		'title'			 => esc_html__( 'Below Header', 'demir' ),
		'panel'			 => 'demir_panel_colors',
		'priority'		 => 10,
	) );

	$wp_customize->add_section( 'demir_color_section_navigation', array(
		'title'			 => esc_html__( 'Navigation', 'demir' ),
		'panel'			 => 'demir_panel_colors',
		'priority'		 => 10,
	) );

	$wp_customize->add_section( 'demir_color_section_general', array(
		'title'			 => esc_html__( 'General', 'demir' ),
		'panel'			 => 'demir_panel_colors',
		'priority'		 => 10,
	) );

	$wp_customize->add_section( 'demir_color_section_woocommerce', array(
		'title'			 => esc_html__( 'WooCommerce', 'demir' ),
		'panel'			 => 'demir_panel_colors',
		'priority'		 => 10,
	) );

	$wp_customize->add_section( 'demir_color_section_footer', array(
		'title'			 => esc_html__( 'Footer', 'demir' ),
		'panel'			 => 'demir_panel_colors',
		'priority'		 => 10,
	) );
}

add_action( 'customize_register', 'demir_kirki_section_color' );


