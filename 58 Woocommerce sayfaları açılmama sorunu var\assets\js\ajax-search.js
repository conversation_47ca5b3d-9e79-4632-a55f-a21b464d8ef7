/**
 * AJAX Search Functionality for WooCommerce Products
 */
(function($) {
    'use strict';

    let searchTimeout;
    let currentRequest;
    let selectedIndex = -1;
    let popularSearchesCache = null; // Cache for popular searches

    const searchContainer = $('.ajax-search-container');
    const searchInput = $('#ajax-search-input');
    const searchResultsDropdown = $('#ajax-search-results');
    const searchLoading = $('#ajax-search-loading');
    const searchForm = $('.search-form');
    const searchIconBtn = $('.search-icon-btn i');
    const searchIconButton = $('.search-icon-btn');

    // Initialize search functionality
    function initAjaxSearch() {
        if (!searchInput.length) {
            return;
        }

        // Create overlay element
        if (!$('.search-overlay').length) {
            $('body').append('<div class="search-overlay"></div>');
        }

        // Bind events
        searchInput.on('input', handleSearchInput);
        searchInput.on('keydown', handleKeyNavigation);
        searchInput.on('focus', handleSearchFocus);
        searchInput.on('click', handleSearchClick); // Click event eklendi
        searchInput.on('blur', handleSearchBlur);
        searchIconButton.on('click', handleIconClick);
        
        // Close dropdown when clicking outside or on overlay
        $(document).on('click', function(e) {
            if (!searchContainer.is(e.target) && searchContainer.has(e.target).length === 0) {
                hideSearchResults();
                hideOverlay();
            }
        });

        // Handle overlay click
        $(document).on('click', '.search-overlay', function() {
            hideSearchResults();
            hideOverlay();
            searchInput.blur();
        });

        // Handle ESC key
        $(document).on('keydown', function(e) {
            if (e.keyCode === 27) { // ESC key
                hideSearchResults();
                hideOverlay();
                searchInput.blur();
            }
        });

        // Prevent form submission if dropdown is open
        searchForm.on('submit', function(e) {
            if (searchResultsDropdown.hasClass('show') && selectedIndex >= 0) {
                e.preventDefault();
                selectCurrentItem();
            }
        });
    }

    // Handle search input
    function handleSearchInput() {
        const query = searchInput.val().trim();

        // Update icon based on input
        updateSearchIcon(query);

        // Clear previous timeout
        clearTimeout(searchTimeout);

        // Cancel previous request
        if (currentRequest) {
            currentRequest.abort();
        }

        if (query.length === 0) {
            hideSearchResults();
            return;
        }

        // Show loading after a short delay
        searchTimeout = setTimeout(function() {
            if (query.length >= 1) {
                performSearch(query);
            }
        }, 300); // 300ms debounce
    }

    // Handle keyboard navigation
    function handleKeyNavigation(e) {
        const items = searchResultsDropdown.find('.ajax-search-item');
        
        switch(e.keyCode) {
            case 40: // Down arrow
                e.preventDefault();
                selectedIndex = Math.min(selectedIndex + 1, items.length - 1);
                updateSelection(items);
                break;
                
            case 38: // Up arrow
                e.preventDefault();
                selectedIndex = Math.max(selectedIndex - 1, -1);
                updateSelection(items);
                break;
                
            case 13: // Enter
                if (selectedIndex >= 0 && items.length > 0) {
                    e.preventDefault();
                    selectCurrentItem();
                }
                break;
        }
    }

    // Handle search focus
    function handleSearchFocus() {
        showOverlay();
        searchContainer.addClass('focused');

        const query = searchInput.val().trim();
        if (query.length >= 1 && searchResultsDropdown.find('.ajax-search-item').length > 0) {
            showSearchResults();
        } else if (query.length === 0) {
            // Show popular searches immediately
            loadPopularSearches();
        }
    }

    // Handle search click - immediate response
    function handleSearchClick() {
        // Immediately show overlay and focus state
        showOverlay();
        searchContainer.addClass('focused');

        const query = searchInput.val().trim();
        if (query.length === 0) {
            // If cached, show immediately
            if (popularSearchesCache) {
                displayPopularSearches(popularSearchesCache);
                showSearchResults();
            } else {
                // Load popular searches
                loadPopularSearches();
            }
        } else if (searchResultsDropdown.find('.ajax-search-item').length > 0) {
            showSearchResults();
        }
    }

    // Handle search blur
    function handleSearchBlur() {
        // Delay to allow clicks on dropdown
        setTimeout(function() {
            if (!searchContainer.is(':focus-within')) {
                hideOverlay();
                searchContainer.removeClass('focused');
            }
        }, 150);
    }

    // Perform AJAX search
    function performSearch(query) {
        showLoading();

        currentRequest = $.ajax({
            url: dmrthema_ajax_search.ajax_url,
            type: 'POST',
            data: {
                action: 'dmrthema_ajax_search',
                query: query,
                nonce: dmrthema_ajax_search.nonce
            },
            success: function(response) {
                hideLoading();

                if (response.success && response.data) {
                    displaySearchResults(response.data, query);
                } else {
                    displayNoResults();
                }
            },
            error: function(xhr, status, error) {
                hideLoading();
                if (status !== 'abort') {
                    displayNoResults();
                }
            },
            complete: function() {
                currentRequest = null;
            }
        });
    }

    // Display search results
    function displaySearchResults(data, query) {
        let html = '';
        
        if (data.products && data.products.length > 0) {
            data.products.forEach(function(product) {
                html += buildProductItem(product, query);
            });
            
            // Add "View all results" link
            if (data.total > data.products.length) {
                html += '<a href="' + data.search_url + '" class="ajax-search-view-all">';
                html += 'Tum sonuclari gor (' + data.total + ' urun)';
                html += '</a>';
            }
        } else {
            html = '<div class="ajax-search-no-results">Aradiginiz kriterlere uygun urun bulunamadi.</div>';
        }
        
        searchResultsDropdown.find('.ajax-search-results-inner').html(html);
        showSearchResults();
        resetSelection();

        // Bind click events to result items
        searchResultsDropdown.find('.ajax-search-item').on('click', function(e) {
            e.preventDefault();
            window.location.href = $(this).attr('href');
        });
    }

    // Build product item HTML
    function buildProductItem(product, query) {
        const highlightedTitle = highlightSearchTerms(product.title, query);
        const imageHtml = product.image ? 
            '<img src="' + product.image + '" alt="' + product.title + '">' :
            '<i class="fas fa-image no-image"></i>';
            
        return '<a href="' + product.url + '" class="ajax-search-item">' +
               '<div class="ajax-search-item-image">' + imageHtml + '</div>' +
               '<div class="ajax-search-item-details">' +
               '<h4 class="ajax-search-item-title">' + highlightedTitle + '</h4>' +
               '<div class="ajax-search-item-price">' + product.price + '</div>' +
               '</div>' +
               '</a>';
    }

    // Highlight search terms in text
    function highlightSearchTerms(text, query) {
        if (!query) return text;
        
        const regex = new RegExp('(' + query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + ')', 'gi');
        return text.replace(regex, '<span class="ajax-search-highlight">$1</span>');
    }

    // Display no results message
    function displayNoResults() {
        const html = '<div class="ajax-search-no-results">Aradiginiz kriterlere uygun urun bulunamadi.</div>';
        searchResultsDropdown.find('.ajax-search-results-inner').html(html);
        showSearchResults();
        resetSelection();
    }

    // Show search results dropdown
    function showSearchResults() {
        searchResultsDropdown.addClass('show');
        selectedIndex = -1;
    }

    // Hide search results dropdown
    function hideSearchResults() {
        searchResultsDropdown.removeClass('show');
        resetSelection();
    }

    // Show loading indicator
    function showLoading() {
        searchLoading.addClass('show');
    }

    // Hide loading indicator
    function hideLoading() {
        searchLoading.removeClass('show');
    }

    // Update selection highlighting
    function updateSelection(items) {
        items.removeClass('keyboard-highlighted');
        
        if (selectedIndex >= 0 && selectedIndex < items.length) {
            const selectedItem = items.eq(selectedIndex);
            selectedItem.addClass('keyboard-highlighted');
            
            // Scroll to selected item if needed
            const container = searchResultsDropdown;
            const itemTop = selectedItem.position().top;
            const itemBottom = itemTop + selectedItem.outerHeight();
            const containerHeight = container.height();
            const scrollTop = container.scrollTop();

            if (itemBottom > containerHeight) {
                container.scrollTop(scrollTop + itemBottom - containerHeight);
            } else if (itemTop < 0) {
                container.scrollTop(scrollTop + itemTop);
            }
        }
    }

    // Select current highlighted item
    function selectCurrentItem() {
        const items = searchResultsDropdown.find('.ajax-search-item');
        if (selectedIndex >= 0 && selectedIndex < items.length) {
            const selectedItem = items.eq(selectedIndex);
            window.location.href = selectedItem.attr('href');
        }
    }

    // Reset selection
    function resetSelection() {
        selectedIndex = -1;
        searchResultsDropdown.find('.ajax-search-item').removeClass('keyboard-highlighted');
    }

    // Update search icon based on input
    function updateSearchIcon(query) {
        if (query.length > 0) {
            searchIconBtn.removeClass('fa-search').addClass('fa-times');
        } else {
            searchIconBtn.removeClass('fa-times').addClass('fa-search');
        }
    }

    // Handle icon click
    function handleIconClick(e) {
        e.preventDefault();

        if (searchIconBtn.hasClass('fa-times')) {
            // Clear search
            searchInput.val('');
            updateSearchIcon('');
            hideSearchResults();
            loadPopularSearches(); // Show popular searches again
            searchInput.focus();
        } else {
            // Perform search
            const query = searchInput.val().trim();
            if (query.length >= 1) {
                performSearch(query);
            }
        }
    }

    // Show overlay
    function showOverlay() {
        $('.search-overlay').addClass('show');
    }

    // Hide overlay
    function hideOverlay() {
        $('.search-overlay').removeClass('show');
    }

    // Load popular searches
    function loadPopularSearches() {
        // If cached, show immediately
        if (popularSearchesCache) {
            displayPopularSearches(popularSearchesCache);
            showSearchResults();
            return;
        }

        // Show loading state immediately
        searchResultsDropdown.find('.ajax-search-results-inner').html('<div class="loading-popular">Popüler aramalar yükleniyor...</div>');
        showSearchResults();

        $.ajax({
            url: dmrthema_ajax_search.ajax_url,
            type: 'POST',
            data: {
                action: 'dmrthema_get_popular_searches',
                nonce: dmrthema_ajax_search.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    popularSearchesCache = response.data; // Cache the result
                    displayPopularSearches(response.data);
                }
            },
            error: function() {
                // Show fallback message
                searchResultsDropdown.find('.ajax-search-results-inner').html('<div class="no-popular">Popüler aramalar yüklenemedi</div>');
            }
        });
    }

    // Display popular searches
    function displayPopularSearches(data) {
        let html = '<div class="popular-searches-header">Popüler Aramalar</div>';

        if (data.searches && data.searches.length > 0) {
            data.searches.forEach(function(item) {
                html += '<a href="' + item.url + '" class="ajax-search-item popular-search-item">';
                html += '<div class="search-item-content">';
                html += '<i class="fas fa-fire popular-icon"></i>';
                html += '<span class="search-item-title">' + item.title + '</span>';
                html += '</div>';
                html += '</a>';
            });
        }

        searchResultsDropdown.find('.ajax-search-results-inner').html(html);
        showSearchResults();
    }

    // Preload popular searches for faster response
    function preloadPopularSearches() {
        if (!popularSearchesCache) {
            $.ajax({
                url: dmrthema_ajax_search.ajax_url,
                type: 'POST',
                data: {
                    action: 'dmrthema_get_popular_searches',
                    nonce: dmrthema_ajax_search.nonce
                },
                success: function(response) {
                    if (response.success && response.data) {
                        popularSearchesCache = response.data;
                    }
                },
                error: function() {
                    // Silent fail
                }
            });
        }
    }

    // Initialize when document is ready
    $(document).ready(function() {
        initAjaxSearch();
        // Preload popular searches for instant response
        setTimeout(preloadPopularSearches, 1000); // 1 saniye sonra yukle
    });

})(jQuery);
