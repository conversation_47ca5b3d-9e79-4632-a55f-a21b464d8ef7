<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo( 'charset' ); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php wp_title( '|', true, 'right' ); ?></title>
    <?php wp_head(); ?>
</head>
<body <?php body_class(); ?>>

<header class="site-header">
    <div class="header-top">
        <div class="container">
            <div class="logo">
                <a href="<?php echo esc_url( home_url( '/' ) ); ?>" rel="home">
                    <img src="<?php echo get_template_directory_uri(); ?>/Logo.png" alt="<?php bloginfo( 'name' ); ?>" class="site-logo">
                </a>
            </div>
            <div class="search-form-container">
                <?php get_search_form(); ?>
            </div>
            <div class="header-right">
                <div class="location">
                    <!-- Konum ikonu ve metni eklenebilir -->
                    <span>Konum</span>
                </div>
                <div class="user-actions">
                    <?php if ( is_user_logged_in() ) : ?>
                        <?php
                        $current_user = wp_get_current_user();
                        $user_avatar = get_avatar( $current_user->ID, 40, '', $current_user->display_name, array( 'class' => 'user-avatar' ) );
                        ?>
                        <div class="user-profile-dropdown">
                            <button class="user-avatar-button" id="user-avatar-toggle">
                                <?php echo $user_avatar; ?>
                            </button>
                            <div class="user-dropdown-menu" id="user-dropdown-menu">
                                <div class="user-info">
                                    <span class="user-name"><?php echo esc_html( $current_user->display_name ); ?></span>
                                    <span class="user-email"><?php echo esc_html( $current_user->user_email ); ?></span>
                                </div>
                                <div class="user-menu-items">
                                    <a href="<?php echo get_permalink( get_option('woocommerce_myaccount_page_id') ); ?>" class="user-menu-item">Hesabim</a>
                                    <a href="<?php echo wp_logout_url( home_url() ); ?>" class="user-menu-item logout">Cikis Yap</a>
                                </div>
                            </div>
                        </div>
                    <?php else : ?>
                        <a href="<?php echo get_permalink( get_option('woocommerce_myaccount_page_id') ); ?>" class="login-button">Giris Yap <span>veya uye ol</span></a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <div class="header-bottom">
        <div class="container">
            <nav class="main-navigation">
                <?php
                wp_nav_menu( array(
                    'theme_location' => 'primary',
                    'menu_id'        => 'primary-menu',
                    'walker'         => new Dmr_Walker_Nav_Menu(),
                ) );
                ?>
            </nav>
            <div class="cart">
                <button class="cart-button" id="cart-toggle">
                    <svg class="cart-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V16.5M9 19.5C9.8 19.5 10.5 20.2 10.5 21S9.8 22.5 9 22.5 7.5 21.8 7.5 21 8.2 19.5 9 19.5ZM20 19.5C20.8 19.5 21.5 20.2 21.5 21S20.8 22.5 20 22.5 18.5 21.8 18.5 21 19.2 19.5 20 19.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <?php if ( class_exists( 'WooCommerce' ) && WC()->cart ) : ?>
                        <span class="cart-count"><?php echo WC()->cart->get_cart_contents_count(); ?></span>
                    <?php endif; ?>
                </button>
            </div>
        </div>
    </div>
</header>

<!-- Sepet Sidebar Overlay -->
<div class="cart-sidebar-overlay" id="cart-sidebar-overlay"></div>

<!-- Sepet Sidebar -->
<div id="cart-sidebar" class="cart-sidebar">
    <div class="cart-sidebar-content">
        <div class="cart-sidebar-header">
            <h3>Sepetim</h3>
            <button class="cart-sidebar-close" id="cart-sidebar-close">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>
        </div>
        <div class="cart-sidebar-body">
            <?php if ( class_exists( 'WooCommerce' ) ) : ?>
                <div class="widget_shopping_cart_content">
                    <?php woocommerce_mini_cart(); ?>
                </div>
            <?php else : ?>
                <p>WooCommerce eklentisi aktif degil.</p>
            <?php endif; ?>
        </div>
        <div class="cart-sidebar-footer">
            <div class="cart-footer-info">
                <p><small>Ucretsiz kargo 500 TL ve uzeri alisverislerde</small></p>
            </div>
        </div>
    </div>
</div>
