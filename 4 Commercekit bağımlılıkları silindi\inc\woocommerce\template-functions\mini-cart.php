<?php
/**
 * Mini Cart Template Functions and Hooks.
 *
 * This file contains functions and hooks specific to the mini cart functionality.
 * 
 * @package demir
 * 
 * Function Index:
 * - demir_always_show_cart() - Controls cart widget visibility
 * - demir_header_cart_drawer() - Renders the slide-out cart drawer
 * - demir_remove_view_cart_minicart() - Manages view cart button visibility
 * - demir_empty_mini_cart() - Handles empty cart state display
 * - demir_sidebar_cart_below_text() - Displays custom text below cart
 * - add_minicart_quantity_fields() - Adds quantity controls to cart items
 * - minicart_demir_update_mini_cart() - Handles AJAX quantity updates
 * - minicart_demir_get_scripts() - Enqueues required scripts
 */

declare(strict_types=1);

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

/**
 * Cart Visibility Functions
 * ------------------------
 */

if ( ! function_exists( 'demir_always_show_cart' ) ) {
	/**
	 * Controls cart widget visibility across all pages.
	 * 
	 * By default, WooCommerce hides the cart widget on cart and checkout pages.
	 * This filter overrides that behavior to always show the mini cart.
	 * 
	 * @since 1.0.0
	 * @return bool Always returns false to ensure cart widget is visible
	 */
	function demir_always_show_cart(): bool {
		return false;
	}
}

// Priority 40 ensures this runs after WooCommerce's default cart visibility logic
add_filter( 'woocommerce_widget_cart_is_hidden', 'demir_always_show_cart', 40, 0 );

/**
 * Cart Drawer Functions
 * --------------------
 */

if ( ! function_exists( 'demir_header_cart_drawer' ) ) {
	/**
	 * Renders the slide-out cart drawer.
	 *
	 * Displays a modal-style drawer containing the cart contents, loading state,
	 * cart title, and close button.
	 *
	 * @since  1.0.0
	 * @uses   demir_is_woocommerce_activated() Check if WooCommerce is activated
	 * @return void
	 */
	function demir_header_cart_drawer(): void {
		// Early return if WooCommerce isn't active
		if ( ! demir_is_woocommerce_activated() ) {
			return;
		}

		$demir_cart_title = demir_get_option( 'demir_cart_title' );
		$class = is_cart() ? 'current-menu-item' : '';

		?>
		<div tabindex="-1" id="demirCartDrawer" class="demir-mini-cart-wrap" role="dialog" aria-modal="true" aria-label="<?php esc_attr_e( 'Cart drawer', 'demir' ); ?>">
			<div id="ajax-loading" 
				 role="status" 
				 aria-live="polite" 
				 aria-label="<?php esc_attr_e('Loading cart contents', 'demir'); ?>">
				<div class="demir-loader">
					<div class="spinner" aria-hidden="true">
						<div class="bounce1"></div>
						<div class="bounce2"></div>
						<div class="bounce3"></div>
					</div>
				</div>
			</div>
			<div class="cart-drawer-heading"><?php echo esc_html( $demir_cart_title ); ?></div>
			<button type="button" aria-label="<?php esc_attr_e( 'Close drawer', 'demir' ); ?>" class="close-drawer">
				<span aria-hidden="true">
					<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1"><path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path></svg>
				</span>
			</button>

			<?php the_widget( 'WC_Widget_Cart', 'title=' ); ?>

		</div>
		<?php

		// Enqueue cart drawer script
		wp_enqueue_script(
			'demir-cart-drawer',
			get_theme_file_uri( '/assets/js/cart-drawer.js' ),
			array( 'jquery' ),
			time(),
			true
		);
	}
}

/**
 * Conditionally removes the "View Cart" button from the mini cart.
 * 
 * @since 1.0.0
 * @return void
 */
function demir_remove_view_cart_minicart() {
    // Get the option directly - no need for empty initialization
    $hide_cart_link = demir_get_option( 'demir_sidebar_hide_cart_link' );
    
    // Early return if the option isn't enabled
    if ( ! $hide_cart_link ) {
        return;
    }
    
    remove_action( 
        'woocommerce_widget_shopping_cart_buttons', 
        'woocommerce_widget_shopping_cart_button_view_cart', 
        10 
    );
}

// Priority 1 ensures this runs before the button is added
add_action( 'woocommerce_widget_shopping_cart_buttons', 'demir_remove_view_cart_minicart', 1 );


if ( ! function_exists( 'demir_empty_mini_cart' ) ) {
    /**
     * Display empty mini cart content
     * Shows the empty mini cart widget area if there are no items in the cart.
     *
     * @since   2.5.4
     * @return  void
     */
    function demir_empty_mini_cart() {
        // Early return if cart is not empty
        if ( ! WC()->cart->is_empty() ) {
            return;
        }

        // Check and display empty mini cart sidebar
        if ( is_active_sidebar( 'empty-mini-cart' ) ) {
            echo '<div class="demir-empty-mini-cart">';
            dynamic_sidebar( 'empty-mini-cart' );
            echo '</div>';
        }
    }
}
add_action( 'woocommerce_before_mini_cart', 'demir_empty_mini_cart', 20 );


if ( ! function_exists( 'demir_sidebar_cart_below_text' ) ) {
    /**
     * Display Below text area Cart Drawer
     *
     * Adds a customizable text area below the mini cart buttons.
     * Text content is managed through theme options.
     *
     * @since 1.0.0
     * @return void
     */
    function demir_sidebar_cart_below_text() {
        $demir_cart_below_text = demir_get_option( 'demir_cart_below_text' );

        if ( ! empty( $demir_cart_below_text ) ) {
            echo '<div class="cart-drawer-below">';
            echo wp_kses_post( $demir_cart_below_text );
            echo '</div>';
        }
    }
}
add_action( 'woocommerce_widget_shopping_cart_after_buttons', 'demir_sidebar_cart_below_text', 10 );

/**
 *  Quantity selectors for demir mini cart
 *
 * @package demir
 *
 * Description: Adds quantity buttons for the demir mini cart
 * Version: 1.0
 * Author: CommerceGurus
 */

// Initialize theme option once, outside the function
$demir_minicart_quantity = demir_get_option( 'demir_minicart_quantity' );

/**
 * Add minicart quantity fields
 *
 * @param  string $html          cart html.
 * @param  string $cart_item     cart item.
 * @param  string $cart_item_key cart item key.
 */
function add_minicart_quantity_fields($html, $cart_item, $cart_item_key) {
    $product_price = apply_filters('woocommerce_cart_item_price', WC()->cart->get_product_price($cart_item['data']), $cart_item, $cart_item_key);
    $_product = apply_filters('woocommerce_cart_item_product', $cart_item['data'], $cart_item, $cart_item_key);
    $max_qty = $_product->get_max_purchase_quantity();
    
    if ($_product->is_sold_individually()) {
        return $product_price;
    }
    
    $max_qty_attr = (-1 !== $max_qty) ? sprintf('max="%d"', $max_qty) : '';
    $product_name = $_product->get_name();
    
    // Enhanced accessibility attributes
    $quantity_html = sprintf(
        '<div class="demir-custom-quantity-mini-cart_container">
            <div class="demir-custom-quantity-mini-cart" role="spinbutton">
                <button type="button" 
                        class="demir-custom-quantity-mini-cart_button quantity-down" 
                        aria-label="%s">-</button>
                <input type="number" 
                       class="demir-custom-quantity-mini-cart_input" 
                       data-cart_item_key="%s" 
                       min="0" 
                       %s 
                       step="1" 
                       value="%d"
                       aria-label="%s"
                       aria-live="polite">
                <button type="button" 
                        class="demir-custom-quantity-mini-cart_button quantity-up" 
                        aria-label="%s">+</button>
            </div>
        </div>',
        esc_attr(sprintf(__('Decrease quantity for %s', 'demir'), $product_name)),
        esc_attr($cart_item_key),
        $max_qty_attr,
        $cart_item['quantity'],
        esc_attr(sprintf(__('Quantity for %s', 'demir'), $product_name)),
        esc_attr(sprintf(__('Increase quantity for %s', 'demir'), $product_name))
    );

    return $product_price . $quantity_html;
}

// Only add the filter if the theme option is enabled
if ( true === $demir_minicart_quantity ) {
	add_filter( 'woocommerce_widget_cart_item_quantity', 'add_minicart_quantity_fields', 10, 3 );
}

if ( ! function_exists( 'minicart_demir_update_mini_cart' ) ) {
    /**
     * Updates mini cart quantities via AJAX.
     * 
     * @since 1.0.0
     * @return void
     */
    function minicart_demir_update_mini_cart() {
        // Add nonce check but make it non-blocking initially
        if (isset($_POST['nonce']) && !wp_verify_nonce($_POST['nonce'], 'demir_update_mini_cart_nonce')) {
            error_log('Invalid nonce in mini cart update - will enforce in future version');
        }
        
        $formdata = isset($_POST['data']) ? (array) $_POST['data'] : array();
        
        if ($formdata) {
            foreach ($formdata as $cart_item_key => $quantity) {
                // Sanitize inputs
                $cart_item_key = sanitize_text_field($cart_item_key);
                $quantity = absint($quantity);
                
                // Validate cart item exists
                if (isset(WC()->cart->get_cart()[$cart_item_key])) {
                    WC()->cart->set_quantity($cart_item_key, $quantity);
                }
            }
        }
        
        WC()->cart->calculate_totals();
        
        $data = array(
            'fragments' => apply_filters('woocommerce_add_to_cart_fragments', array()),
            'success' => true
        );
        
        wp_send_json($data);
    }
}

// Add AJAX actions for both logged in and non-logged in users
add_action( 'wp_ajax_cg_demir_update_mini_cart', 'minicart_demir_update_mini_cart' );
add_action( 'wp_ajax_nopriv_cg_demir_update_mini_cart', 'minicart_demir_update_mini_cart' );


if ( ! function_exists( 'minicart_demir_get_scripts' ) ) {
    /**
     * Enqueue mini-cart quantity scripts
     */
    function minicart_demir_get_scripts() {
        wp_enqueue_script(
            'custom-demir-quantity-js',
            get_theme_file_uri('/assets/js/minicart-quantity_v2.js'),
            array('jquery'),
            time(),
            true
        );

        wp_localize_script(
            'custom-demir-quantity-js',
            'demir_mini_cart',
            array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('demir_update_mini_cart_nonce'),
                'updating_text' => esc_html__('Updating cart...', 'demir'),
                'error_text' => esc_html__('Error updating cart.', 'demir')
            )
        );
    }
}
add_action('wp_enqueue_scripts', 'minicart_demir_get_scripts', 30);


