<?php
/**
 * demir functions.
 *
 * @package demir
 */

if ( ! function_exists( 'demir_is_woocommerce_activated' ) ) {
	/**
	 * Query WooCommerce activation
	 */
	function demir_is_woocommerce_activated() {
		return class_exists( 'WooCommerce' ) ? true : false;
	}
}

if ( ! function_exists( 'demir_is_v2_enabled' ) ) {
	/**
	 * Check if v2 modular template functions are enabled
	 * 
	 * @return boolean
	 */
	function demir_is_v2_enabled() {
		// Check for constant first
		if ( defined( 'DEMIR_ENABLE_V2' ) ) {
			return (bool) DEMIR_ENABLE_V2;
		}
		return false;
	}
}

/**
 * Preload the icon font files.
 */
add_action('wp_head', 'demir_preload_icon_fonts');

	function demir_preload_icon_fonts() { 

	$demir_general_speed_rivolicons = '';
	$demir_general_speed_rivolicons = demir_get_option( 'demir_general_speed_rivolicons' );

	if ( 'yes' === $demir_general_speed_rivolicons ) { ?>
		<link rel="preload" href="<?php echo esc_url( get_template_directory_uri() ) ; ?>/assets/fonts/Rivolicons-Free.woff2?-uew922" as="font" type="font/woff2" crossorigin="anonymous">
	<?php }
	?>

<?php }


/**
 * Produces nice safe html for presentation.
 *
 * @param $input - accepts a string.
 * @return string
 */
function demir_safe_html( $input ) {

	$args = array(
		// formatting.
		'span'   => array(
			'class' => array(),
		),
		'h1'     => array(
			'class' => array(),
		),
		'h2'     => array(
			'class' => array(),
		),
		'h3'     => array(
			'class' => array(),
		),
		'h4'     => array(
			'class' => array(),
		),
		'del'    => array(),
		'ins'    => array(),
		'strong' => array(),
		'em'     => array(),
		'b'      => array(),
		'hr'     => array(),
		'i'      => array(
			'class' => array(),
		),
		'img'      => array(
			'href'        	=> array(),
			'alt'         	=> array(),
			'class'       	=> array(),
			'scale'       	=> array(),
			'width'       	=> array(),
			'height'      	=> array(),
			'src'         	=> array(),
			'srcset'      	=> array(),
			'sizes'       	=> array(),
			'data-src'    	=> array(),
			'data-srcset' 	=> array(),
			'loading'		=> array(),
		),
		'p'     => array(
			'class' => array(),
		),
		'figure'     => array(
			'class' => array(),
		),
		'div'     => array(
			'class' => array(),
			'style' => array(),
		),
		'ul'     => array(
			'class' => array(),
		),
		'li'     => array(
			'class' => array(),
		),
		'mark'   => array(
			'class' => array(),
		),

		// links.
		'a'        => array(
			'href'            => array(),
			'data-product-id' => array(),
			'data-type'       => array(),
			'data-wpage'      => array(),
			'class'           => array(),
			'aria-label'      => array(),
			'target'          => array(),
		),
	);

	return wp_kses( $input, $args );
}

/**
 * Returns a shortcode for the menu.
 */
function demir_menu_enable_shortcode( $menu, $args ) {
		return do_shortcode( $menu );
}

/**
 * Include the filter inside an action.
 */
if ( ! function_exists( 'demir_menu_load_shortcode' ) ) {
	function demir_menu_load_shortcode() {
		add_filter( 'wp_nav_menu', 'demir_menu_enable_shortcode', 20, 2 );
	}
}
add_action( 'demir_header', 'demir_menu_load_shortcode', 42 );


/**
 * Primary Menu Custom Walker - add a wrapper div.
 */
class submenuwrap extends Walker_Nav_Menu {

	function start_lvl( &$output, $depth = 0, $args = array() ) {
		$indent  = str_repeat( "\t", $depth );
		$output .= "\n$indent<div class='sub-menu-wrapper'><div class='container'><ul class='sub-menu'>\n";
	}
	function end_lvl( &$output, $depth = 0, $args = array() ) {
		$indent  = str_repeat( "\t", $depth );
		$output .= "$indent</ul></div></div>\n";
	}
	function start_el( &$output, $item, $depth = 0, $args = array(), $id = 0 ) {

		$demir_menu_display_description = '';
		$demir_menu_display_description = demir_get_option( 'demir_menu_display_description' );

		$indent = ( $depth > 0 ? str_repeat( "\t", $depth ) : '' );

		// Passed Classes
		$classes     = empty( $item->classes ) ? array() : (array) $item->classes;
		$class_names = esc_attr( implode( ' ', apply_filters( 'nav_menu_css_class', array_filter( $classes ), $item ) ) );

		// build html
		$output .= sprintf( '<li id="nav-menu-item-' . $item->ID . '" class="' . $class_names . '" %s>',
			in_array( 'menu-item-has-children', $item->classes ) ? ' aria-haspopup="true" aria-expanded="false"' : ''
		);

		// If 'menu-item-product' or 'nolink' exists in menu classes, don't add the HTML anchor tag.
		if ( in_array( 'menu-item-product', $classes ) ) {
			$item_output = apply_filters( 'the_title', $item->title, $item->ID );
		}
		elseif ( in_array( 'nolink', $classes ) ) {
			$item_output = '<span>' . apply_filters( 'the_title', $item->title, $item->ID ) . '</span>';
		}
		else {
			// link attributes.
			$attributes  = ! empty( $item->attr_title ) ? ' title="' . esc_attr( $item->attr_title ) . '"' : '';
			$attributes .= ! empty( $item->target ) ? ' target="' . esc_attr( $item->target ) . '"' : '';
			$attributes .= ! empty( $item->xfn ) ? ' rel="' . esc_attr( $item->xfn ) . '"' : '';
			$attributes .= ! empty( $item->url ) ? ' href="' . esc_attr( $item->url ) . '"' : '';
			$attributes .= ' class="cg-menu-link ' . ( $depth > 0 ? 'sub-menu-link' : 'main-menu-link' ) . '"';

			$description = ( ! empty ( $item->description ) and 1 == $depth )
            ? '<span class="sub">'.  $item->description . '</span>' : '';

            // Display menu descriptions
            if ( true === $demir_menu_display_description ) { 

			$item_output = sprintf(
				'%1$s<a%2$s>%3$s%4$s%5$s%6$s</a>',
				$args->before,
				$attributes,
				$args->link_before,
				apply_filters( 'the_title', $item->title, $item->ID ),
				$description,
				$args->link_after,
				$args->after
			);

			}

			// Do not display menu descriptions
			else {
				$item_output = sprintf(
				'%1$s<a%2$s>%3$s%4$s%5$s</a>%6$s',
				$args->before,
				$attributes,
				$args->link_before,
				apply_filters( 'the_title', $item->title, $item->ID ),
				$args->link_after,
				$args->after
			);

			}
		}

		// build html.
		$output .= apply_filters( 'walker_nav_menu_start_el', $item_output, $item, $depth, $args );
	}

}

/**
 * Allow HTML in Menu Descriptions.
 */
remove_filter( 'nav_menu_description', 'strip_tags' );

function demir_html_menu_descriptions( $menu_item ) {
    if ( isset( $menu_item->post_type ) ) {
        if ( 'nav_menu_item' == $menu_item->post_type ) {
            $menu_item->description = apply_filters( 'nav_menu_description', $menu_item->post_content );
        }
    }
    return $menu_item;
}

add_filter( 'wp_setup_nav_menu_item', 'demir_html_menu_descriptions' );

/**
 * Enables the display of menu descriptions.
 */
function demir_prefix_nav_description( $item_output, $item, $depth, $args ) {
    if ( !empty( $item->description ) ) {
        $item_output = str_replace($args->link_before.'</a>',
            '<div class="icon-wrapper">'.$item->description.'</div>'.$args->link_before.'</a>',
            $item_output
        );
    }

    return $item_output;
}
add_filter( 'walker_nav_menu_start_el', 'demir_prefix_nav_description', 10, 4 );

/**
 * Adds a caret icon for the mobile menu.
 */
function demir_mobile_menu_plus( $output, $item, $depth, $args ) {

	if ( ( 'primary' == $args->theme_location ) || ( 'mobile' == $args->theme_location ) ) {
		if ( in_array( 'menu-item-has-children', $item->classes ) ) {
			$output .= '<span class="caret"></span>';
		}
	}
	return $output;
}

add_filter( 'walker_nav_menu_start_el', 'demir_mobile_menu_plus', 10, 4 );


add_filter( 'woocommerce_show_page_title', '__return_false' );
add_action( 'woocommerce_before_main_content', 'demir_archives_title', 20 );

/**
 * Call a shortcode function by tag name.
 *
 * @since  1.0.0
 *
 * @param string $tag     The shortcode whose function to call.
 * @param array  $atts    The attributes to pass to the shortcode function. Optional.
 * @param array  $content The shortcode's content. Default is null (none).
 *
 * @return string|bool False on failure, the result of the shortcode on success.
 */

function demir_do_shortcode( $tag, array $atts = array(), $content = null ) {
	global $shortcode_tags;

	if ( ! isset( $shortcode_tags[ $tag ] ) ) {
		return false;
	}

	return call_user_func( $shortcode_tags[ $tag ], $atts, $content, $tag );
}

