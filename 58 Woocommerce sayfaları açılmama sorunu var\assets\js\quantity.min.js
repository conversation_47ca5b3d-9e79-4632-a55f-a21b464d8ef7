function demirWooQuantityButtons(){var t=document.querySelectorAll(".woocommerce-cart-form div.quantity:not(.buttons_added) .qty, .summary div.quantity:not(.buttons_added) .qty");if(0<t.length){var e=document.createElement("div");e.setAttribute("class","quantity-nav"),e.innerHTML='<span tabindex="0" role="button" aria-label="Increase quantity" class="quantity-button quantity-up plus"></span><span tabindex="0" role="button" aria-label="Reduce quantity" class="quantity-button quantity-down minus"></span>',t.forEach(function(t){if("date"!==t.type&&"hidden"!==t.type){t.parentNode.classList.add("buttons_added");var n=e.cloneNode(!0);t.parentNode.insertBefore(n,t.nextSibling)}});var n=document.querySelectorAll("input.qty");0<n.length&&n.forEach(function(t){if(!t.closest(".product-quantity")){var e=parseInt(t.getAttribute("min"));e&&0<e&&parseInt(t.value)<e&&(t.value=e)}})}}jQuery(document).ready(function(t){t(document).ajaxComplete(function(){demirWooQuantityButtons()}),t(document.body).on("wc_fragments_refreshed",function(){demirWooQuantityButtons()})}),document.addEventListener("DOMContentLoaded",function(){window.addEventListener("load",function(t){demirWooQuantityButtons()})}),document.addEventListener("DOMContentLoaded",function(){document.addEventListener("click",function(t){var e=t.target;if(e.classList.contains("plus")||e.classList.contains("minus")){var n=e.closest(".quantity");if(n){var a=n.querySelector("input.qty");if(a){var i=parseInt(a.value),o=parseInt(a.getAttribute("max")),u=parseInt(a.getAttribute("min")),r=a.getAttribute("step");i&&""!==i&&"NaN"!==i||(i=0),(""===o||"NaN"===o)&&(o=""),(""===u||"NaN"===u)&&(u=0),("any"===r||""===r||void 0===r||"NaN"===parseInt(r))&&(r=1),e.classList.contains("plus")?o&&(o===i||i>o)?a.value=o:a.value=i+parseInt(r):u&&(u===i||i<u)?a.value=u:0<i&&(a.value=i-parseInt(r));var s=document.querySelector('.woocommerce-cart-form [name="update_cart"]');s&&(s.removeAttribute("disabled"),s.setAttribute("aria-disabled","false")),a.dispatchEvent(new Event("change",{bubbles:!0}))}}t.stopPropagation(),t.preventDefault();return}})});
